# First we will import the necessary Library

import os
import pandas as pd
import numpy as np
import math
import datetime as dt
import matplotlib.pyplot as plt

# For Evalution we will use these library

from sklearn.metrics import mean_squared_error, mean_absolute_error, explained_variance_score, r2_score
from sklearn.metrics import mean_poisson_deviance, mean_gamma_deviance, accuracy_score
from sklearn.preprocessing import MinMaxScaler

# For model building we will use these library

import tensorflow as tf
from tensorflow.keras.models import Sequential, load_model, Model
from tensorflow.keras.layers import Dense, Dropout, Input, Average
from tensorflow.keras.layers import LSTM, GRU
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
from tensorflow.keras.optimizers import Adam, Nadam


# For PLotting we will use these library

import matplotlib.pyplot as plt
from itertools import cycle
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots

"""# 3. Loading Dataset"""

# Function to clean up the price values (remove commas and quotes)
def clean_price(price_str):
    if isinstance(price_str, str):
        return float(price_str.replace('"', '').replace(',', ''))
    return price_str

# Function to process volume data with K, M, B suffixes
def process_volume(vol_str):
    if isinstance(vol_str, str):
        if 'K' in vol_str:
            return float(vol_str.replace('K', '')) * 1000
        elif 'M' in vol_str:
            return float(vol_str.replace('M', '')) * 1000000
        elif 'B' in vol_str:
            return float(vol_str.replace('B', '')) * 1000000000
        else:
            return float(vol_str)
    return vol_str

# Function to load and preprocess data
def load_data():
    # Load our dataset
    import os
    # Get the path relative to the project root
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)
    data_path = os.path.join(project_root, 'Data', 'Bitcoin Historical Data.csv')
    maindf = pd.read_csv(data_path)

    # Clean numeric columns - they have commas and quotes
    numeric_columns = ['Price', 'Open', 'High', 'Low']
    for col in numeric_columns:
        maindf[col] = maindf[col].apply(clean_price)

    # Handle the 'Vol.' column
    maindf['Volume'] = maindf['Vol.'].apply(process_volume)

    # Convert Date to datetime
    maindf['Date'] = pd.to_datetime(maindf['Date'], format='%m/%d/%Y')

    # Since the data is in reverse chronological order (newest first), sort it chronologically
    maindf = maindf.sort_values('Date')

    print('Total number of days present in the dataset: ', maindf.shape[0])
    print('Total number of fields present in the dataset: ', maindf.shape[1])

    return maindf

# Function to create dataset for LSTM
def create_dataset(dataset, time_step=1):
    dataX, dataY = [], []
    for i in range(len(dataset)-time_step-1):
        a = dataset[i:(i+time_step), 0]
        dataX.append(a)
        dataY.append(dataset[i + time_step, 0])
    return np.array(dataX), np.array(dataY)

# Enhanced LSTM model architecture based on reference strategies
def build_enhanced_lstm_model(window_size, n_features=1):
    """Build enhanced LSTM model with multiple layers supporting multivariate input"""
    model = Sequential()

    # Add Input layer first
    model.add(Input(shape=(window_size, n_features)))

    # First LSTM layer with return_sequences=True
    model.add(LSTM(units=50, return_sequences=True))
    model.add(Dropout(0.2))

    # Second LSTM layer with return_sequences=True
    model.add(LSTM(units=50, return_sequences=True))
    model.add(Dropout(0.2))

    # Third LSTM layer with return_sequences=True
    model.add(LSTM(units=50, return_sequences=True))
    model.add(Dropout(0.2))

    # Fourth LSTM layer without return_sequences
    model.add(LSTM(units=50))
    model.add(Dropout(0.2))

    # Output layer
    model.add(Dense(units=1))

    return model

# Enhanced GRU model architecture
def build_enhanced_gru_model(window_size, n_features=1):
    """Build enhanced GRU model with multiple layers supporting multivariate input"""
    model = Sequential()

    # Add Input layer first
    model.add(Input(shape=(window_size, n_features)))

    # First GRU layer with return_sequences=True
    model.add(GRU(units=50, return_sequences=True))
    model.add(Dropout(0.2))

    # Second GRU layer with return_sequences=True
    model.add(GRU(units=50, return_sequences=True))
    model.add(Dropout(0.2))

    # Third GRU layer with return_sequences=True
    model.add(GRU(units=50, return_sequences=True))
    model.add(Dropout(0.2))

    # Fourth GRU layer without return_sequences
    model.add(GRU(units=50))
    model.add(Dropout(0.2))

    # Output layer
    model.add(Dense(units=1))

    return model

# Advanced model architecture (similar to current gold LSTM)
def build_advanced_lstm_model(window_size, n_features=1):
    """Build advanced LSTM model with functional API supporting multivariate input"""
    input1 = Input(shape=(window_size, n_features))
    x = LSTM(units=64, return_sequences=True)(input1)
    x = Dropout(0.2)(x)
    x = LSTM(units=64, return_sequences=True)(x)
    x = Dropout(0.2)(x)
    x = LSTM(units=64)(x)
    x = Dropout(0.2)(x)
    x = Dense(32, activation='relu')(x)  # Changed from softmax to relu for regression
    dnn_output = Dense(1)(x)
    model = Model(inputs=input1, outputs=[dnn_output])

    return model

# Ensemble model combining LSTM and GRU
def build_ensemble_model(window_size):
    """Build ensemble model combining LSTM and GRU"""
    # LSTM branch
    lstm_input = Input(shape=(window_size, 1), name='lstm_input')
    lstm_x = LSTM(units=50, return_sequences=True)(lstm_input)
    lstm_x = Dropout(0.2)(lstm_x)
    lstm_x = LSTM(units=50)(lstm_x)
    lstm_x = Dropout(0.2)(lstm_x)
    lstm_output = Dense(32, activation='relu')(lstm_x)

    # GRU branch
    gru_input = Input(shape=(window_size, 1), name='gru_input')
    gru_x = GRU(units=50, return_sequences=True)(gru_input)
    gru_x = Dropout(0.2)(gru_x)
    gru_x = GRU(units=50)(gru_x)
    gru_x = Dropout(0.2)(gru_x)
    gru_output = Dense(32, activation='relu')(gru_x)

    # Combine outputs
    combined = Average()([lstm_output, gru_output])
    final_output = Dense(1)(combined)

    model = Model(inputs=[lstm_input, gru_input], outputs=final_output)

    return model

def evaluate_model(model, X, y, scaler, name=""):
    """Evaluate model performance with various metrics"""
    predictions = model.predict(X)

    # Transform back to original form
    predictions = scaler.inverse_transform(predictions)
    original_y = scaler.inverse_transform(y.reshape(-1,1))

    # Calculate various metrics
    rmse = math.sqrt(mean_squared_error(original_y, predictions))
    mae = mean_absolute_error(original_y, predictions)
    r2 = r2_score(original_y, predictions)

    print(f"\n{name} Evaluation Metrics:")
    print(f"RMSE: {rmse:.2f}")
    print(f"MAE: {mae:.2f}")
    print(f"R2 Score: {r2:.4f}")

    return rmse, mae, r2

def create_enhanced_sequences(data, window_size, n_features=1):
    """Create sequences for training with enhanced preprocessing supporting multiple features"""
    X, y = [], []
    for i in range(window_size, len(data)):
        if n_features == 1:
            X.append(data[i-window_size:i, 0])
            y.append(data[i, 0])
        else:
            X.append(data[i-window_size:i, :])
            y.append(data[i, 0])  # Still predict only price
    return np.array(X), np.array(y)

def create_multivariate_sequences(price_data, volume_data, window_size):
    """Create sequences with both price and volume features"""
    X, y = [], []
    for i in range(window_size, len(price_data)):
        # Combine price and volume features
        price_seq = price_data[i-window_size:i, 0]
        volume_seq = volume_data[i-window_size:i, 0]

        # Stack features: [price_seq, volume_seq]
        feature_seq = np.column_stack([price_seq, volume_seq])
        X.append(feature_seq)
        y.append(price_data[i, 0])

    return np.array(X), np.array(y)

def rolling_window_validation(model_builder, X, y, n_splits=5, test_size=0.2):
    """
    Perform rolling window validation for time series data

    Parameters:
    - model_builder: function that returns a compiled model
    - X, y: training data
    - window_size: size of the rolling window
    - n_splits: number of validation splits
    - test_size: proportion of data for testing in each split

    Returns:
    - scores: list of validation scores for each split
    - predictions: list of predictions for each split
    """
    scores = []
    predictions = []

    total_samples = len(X)
    test_samples = int(total_samples * test_size)

    print(f"\n=== Rolling Window Validation ===")
    print(f"Total samples: {total_samples}")
    print(f"Test samples per split: {test_samples}")
    print(f"Number of splits: {n_splits}")

    for i in range(n_splits):
        print(f"\nSplit {i+1}/{n_splits}")

        # Calculate split indices
        split_end = total_samples - (n_splits - i - 1) * (test_samples // 2)

        # Ensure we have enough data for training
        train_end = split_end - test_samples
        train_start = max(0, train_end - (total_samples // n_splits))

        print(f"Train range: {train_start} to {train_end}")
        print(f"Test range: {train_end} to {split_end}")

        # Split data
        X_train_split = X[train_start:train_end]
        y_train_split = y[train_start:train_end]
        X_test_split = X[train_end:split_end]
        y_test_split = y[train_end:split_end]

        if len(X_train_split) == 0 or len(X_test_split) == 0:
            print(f"Skipping split {i+1} due to insufficient data")
            continue

        print(f"Train shape: {X_train_split.shape}, Test shape: {X_test_split.shape}")

        # Build and train model
        model = model_builder()

        # Early stopping for efficiency
        early_stopping = EarlyStopping(monitor='val_loss', patience=10, restore_best_weights=True)

        model.fit(
            X_train_split, y_train_split,
            validation_data=(X_test_split, y_test_split),
            epochs=50,  # Reduced for validation
            batch_size=32,
            callbacks=[early_stopping],
            verbose=0
        )

        # Evaluate
        y_pred = model.predict(X_test_split, verbose=0)
        rmse = np.sqrt(mean_squared_error(y_test_split, y_pred))
        scores.append(rmse)
        predictions.append(y_pred)

        print(f"Split {i+1} RMSE: {rmse:.4f}")

    avg_score = np.mean(scores)
    std_score = np.std(scores)
    print(f"\n=== Rolling Window Validation Results ===")
    print(f"Average RMSE: {avg_score:.4f} ± {std_score:.4f}")
    print(f"Individual scores: {[f'{score:.4f}' for score in scores]}")

    return scores, predictions

def ensemble_predict(models, X):
    """Make ensemble predictions using multiple models"""
    predictions = []
    for model in models:
        pred = model.predict(X, verbose=0)
        predictions.append(pred)

    # Average the predictions
    ensemble_pred = np.mean(predictions, axis=0)
    return ensemble_pred

def enhanced_predict_future(model, scaler, data, window_size, future_days, use_ensemble=False, ensemble_models=None):
    """Enhanced future prediction with optional ensemble"""
    predictions = []
    current_batch = data[-window_size:].copy()

    for i in range(future_days):
        # Reshape for prediction
        current_batch_reshaped = current_batch.reshape((1, window_size, 1))

        # Make prediction
        if use_ensemble and ensemble_models:
            pred = ensemble_predict(ensemble_models, current_batch_reshaped)
        else:
            pred = model.predict(current_batch_reshaped, verbose=0)

        predictions.append(pred[0, 0])

        # Update batch for next prediction
        current_batch = np.append(current_batch[1:], pred[0, 0])

    # Transform back to original scale
    predictions_array = np.array(predictions).reshape(-1, 1)
    predictions_scaled = scaler.inverse_transform(predictions_array)

    return predictions_scaled.flatten()

def train_and_save_model():
    # Load the data
    maindf = load_data()

    # Prepare DataFrame with Price and Volume
    closedf = maindf[['Date', 'Price', 'Volume']].copy()
    closedf['Date'] = pd.to_datetime(closedf['Date'])
    closedf = closedf.sort_values(by='Date', ascending=True).reset_index(drop=True)
    closedf['Price'] = closedf['Price'].astype('float64')
    closedf['Volume'] = closedf['Volume'].astype('float64')
    dates = closedf['Date']

    # Test set: latest 3 years for testing
    latest_date = closedf['Date'].max()
    three_years_ago = latest_date - pd.DateOffset(years=3)
    test_data = closedf[closedf['Date'] >= three_years_ago]
    train_data = closedf[closedf['Date'] < three_years_ago]

    print(f"Total data points: {len(closedf)}")
    print(f"Training data points: {len(train_data)} (from {train_data['Date'].min()} to {train_data['Date'].max()})")
    print(f"Testing data points: {len(test_data)} (from {test_data['Date'].min()} to {test_data['Date'].max()})")

    # Prepare train/test split with multivariate features
    price = closedf['Price']
    volume = closedf['Volume']

    # Separate scalers for price and volume
    price_scaler = MinMaxScaler()
    volume_scaler = MinMaxScaler()

    price_scaler.fit(price.values.reshape(-1,1))
    volume_scaler.fit(volume.values.reshape(-1,1))

    window_size = 60

    print(f"Total data points: {len(price)}")
    print(f"Test data points: {len(test_data)}")
    print(f"Training data points: {len(train_data)}")
    print(f"Window size: {window_size}")
    print(f"Features: Price + Volume (multivariate)")

    # Scalers are fitted and ready for use in data preparation

    # Enhanced data preparation with multivariate features
    # Training data (before 3-year test period)
    train_price = train_data['Price']
    train_volume = train_data['Volume']
    train_price_scaled = price_scaler.transform(train_price.values.reshape(-1,1))
    train_volume_scaled = volume_scaler.transform(train_volume.values.reshape(-1,1))

    # Test data (latest 3 years)
    test_price = test_data['Price']
    test_volume = test_data['Volume']
    test_price_scaled = price_scaler.transform(test_price.values.reshape(-1,1))
    test_volume_scaled = volume_scaler.transform(test_volume.values.reshape(-1,1))

    # Create multivariate sequences
    X_train_mv, y_train_mv = create_multivariate_sequences(train_price_scaled, train_volume_scaled, window_size)
    X_test_mv, y_test_mv = create_multivariate_sequences(test_price_scaled, test_volume_scaled, window_size)

    # Also create univariate sequences for comparison
    X_train_uv, y_train_uv = create_enhanced_sequences(train_price_scaled, window_size, n_features=1)
    X_test_uv, y_test_uv = create_enhanced_sequences(test_price_scaled, window_size, n_features=1)

    # Reshape univariate data for LSTM input
    X_train_uv = np.reshape(X_train_uv, (X_train_uv.shape[0], X_train_uv.shape[1], 1))
    X_test_uv = np.reshape(X_test_uv, (X_test_uv.shape[0], X_test_uv.shape[1], 1))
    y_train_uv = np.reshape(y_train_uv, (-1,1))
    y_test_uv = np.reshape(y_test_uv, (-1,1))

    # Multivariate data is already in correct shape
    y_train_mv = np.reshape(y_train_mv, (-1,1))
    y_test_mv = np.reshape(y_test_mv, (-1,1))

    print('Univariate X_train Shape: ', X_train_uv.shape)
    print('Univariate y_train Shape: ', y_train_uv.shape)
    print('Multivariate X_train Shape: ', X_train_mv.shape)
    print('Multivariate y_train Shape: ', y_train_mv.shape)

    # Training Advanced Multivariate LSTM only
    print("\n=== Training Advanced Multivariate LSTM Model ===")
    print("Testing on latest 3 years of data, then retraining with full dataset...")

    # Step 1: Test Advanced Multivariate LSTM on 3-year test data
    print("\n=== Step 1: Testing Advanced Multivariate LSTM on Latest 3 Years ===")

    # Add callbacks for better training
    early_stopping = EarlyStopping(monitor='val_loss', patience=20, restore_best_weights=True)
    reduce_lr = ReduceLROnPlateau(monitor='val_loss', factor=0.5, patience=10, min_lr=1e-7)

    # Build and train Advanced LSTM model on 3-year test split
    test_model = build_advanced_lstm_model(window_size, n_features=2)
    test_model.compile(loss='mean_squared_error', optimizer='Nadam')

    print("Training on data before 3-year test period...")
    test_history = test_model.fit(
        X_train_mv, y_train_mv,
        validation_data=(X_test_mv, y_test_mv),
        epochs=150,
        batch_size=32,
        callbacks=[early_stopping, reduce_lr],
        verbose=1
    )

    # Evaluate Advanced LSTM on 3-year test data
    print("\n=== Evaluating Advanced LSTM on 3-Year Test Data ===")

    # Make predictions on test data
    test_pred = test_model.predict(X_test_mv, verbose=0)

    # Calculate comprehensive metrics
    from sklearn.metrics import mean_absolute_percentage_error
    test_rmse = np.sqrt(mean_squared_error(y_test_mv, test_pred))
    test_mae = mean_absolute_error(y_test_mv, test_pred)
    test_r2 = r2_score(y_test_mv, test_pred)
    test_mape = mean_absolute_percentage_error(y_test_mv, test_pred)
    test_accuracy = (1 - test_mape) * 100  # Convert to percentage

    print(f"3-Year Test Results:")
    print(f"RMSE: {test_rmse:.4f}")
    print(f"MAE: {test_mae:.4f}")
    print(f"R² Score: {test_r2:.4f}")
    print(f"MAPE: {test_mape:.4f}")
    print(f"Accuracy: {test_accuracy:.2f}%")

    # Step 2: Retrain with full dataset
    print("\n=== Step 2: Retraining Advanced LSTM with Full Dataset ===")

    # Prepare full dataset for training
    full_price = closedf['Price'].values
    full_volume = closedf['Volume'].values

    # Scale full data
    full_price_scaled = price_scaler.fit_transform(full_price.reshape(-1,1))
    full_volume_scaled = volume_scaler.fit_transform(full_volume.reshape(-1,1))

    # Combine features for multivariate
    full_data_mv = np.column_stack((full_price_scaled, full_volume_scaled))

    # Create sequences for full dataset
    X_full_mv, y_full_mv = create_multivariate_sequences(full_price_scaled, full_volume_scaled, window_size)

    print(f"Full dataset shape: X={X_full_mv.shape}, y={y_full_mv.shape}")

    # Build final model with full data
    final_model = build_advanced_lstm_model(window_size, n_features=2)
    final_model.compile(loss='mean_squared_error', optimizer='Nadam')

    print("Training final model with full dataset...")
    final_history = final_model.fit(
        X_full_mv, y_full_mv,
        validation_split=0.2,  # Use 20% for validation
        epochs=150,
        batch_size=32,
        callbacks=[early_stopping, reduce_lr],
        verbose=1
    )

    # Save the final model and scalers to /model folder (root level)
    model_dir = 'model'
    if not os.path.exists(model_dir):
        os.makedirs(model_dir)

    model_save_path = os.path.join(model_dir, 'bitcoin_lstm_model.keras')
    final_model.save(model_save_path)
    print(f"Final model saved to {model_save_path}")

    import joblib
    scaler_save_path = os.path.join(model_dir, 'bitcoin_price_scaler.save')
    joblib.dump(price_scaler, scaler_save_path)
    print(f"Price scaler saved to {scaler_save_path}")

    volume_scaler_path = os.path.join(model_dir, 'bitcoin_volume_scaler.save')
    joblib.dump(volume_scaler, volume_scaler_path)
    print(f"Volume scaler saved to {volume_scaler_path}")

    # Save performance metrics for API access
    metrics = {
        'test_accuracy': test_accuracy,
        'test_rmse': test_rmse,
        'test_mae': test_mae,
        'test_r2': test_r2,
        'test_mape': test_mape,
        'model_type': 'Advanced Multivariate LSTM',
        'training_data_points': len(train_data),
        'test_data_points': len(test_data),
        'features': ['Price', 'Volume']
    }

    metrics_path = os.path.join(model_dir, 'model_metrics.json')
    import json
    with open(metrics_path, 'w') as f:
        json.dump(metrics, f, indent=2)
    print(f"Model metrics saved to {metrics_path}")

    # Plot loss curves for final model
    loss = final_history.history['loss']
    val_loss = final_history.history['val_loss']
    epochs = range(len(loss))
    plt.figure(figsize=(10, 6))
    plt.plot(epochs, loss, 'r', label='Training loss')
    plt.plot(epochs, val_loss, 'b', label='Validation loss')
    plt.title('Advanced LSTM Training and Validation Loss')
    plt.legend(loc=0)
    plt.savefig(os.path.join(model_dir, 'loss_curves.png'))
    plt.close()

    # Final evaluation on full model
    print(f"\n=== Final Model Performance Summary ===")
    print(f"Model Type: Advanced Multivariate LSTM")
    print(f"Features: Price + Volume")
    print(f"3-Year Test Results:")
    print(f"  - RMSE: {test_rmse:.4f}")
    print(f"  - MAE: {test_mae:.4f}")
    print(f"  - R² Score: {test_r2:.4f}")
    print(f"  - MAPE: {test_mape:.4f}")
    print(f"  - Accuracy: {test_accuracy:.2f}%")
    print(f"Training Data: {len(train_data)} points")
    print(f"Test Data: {len(test_data)} points")
    print(f"Final Model trained on: {len(X_full_mv)} sequences")

    # Create visualization for test results
    y_test_true = price_scaler.inverse_transform(y_test_mv.reshape(-1, 1))
    y_test_pred_scaled = price_scaler.inverse_transform(test_pred.reshape(-1, 1))

    # Create comprehensive plots
    plt.figure(figsize=(20, 12))

    # Plot 1: Test prediction comparison
    plt.subplot(2, 2, 1)
    test_dates = test_data['Date'].iloc[window_size:]  # Adjust for window size
    plt.plot(test_dates, y_test_true, color='blue', lw=2, label='Actual Price')
    plt.plot(test_dates, y_test_pred_scaled, color='red', lw=2, label='Predicted Price')
    plt.title('Advanced LSTM: 3-Year Test Results', fontsize=14)
    plt.xlabel('Date', fontsize=12)
    plt.ylabel('Price ($)', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)

    # Plot 2: Loss curves
    plt.subplot(2, 2, 2)
    plt.plot(test_history.history['loss'], label='Training Loss', color='blue')
    plt.plot(test_history.history['val_loss'], label='Validation Loss', color='red')
    plt.title('Training and Validation Loss')
    plt.xlabel('Epochs')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # Plot 3: Prediction accuracy scatter
    plt.subplot(2, 2, 3)
    plt.scatter(y_test_true, y_test_pred_scaled, alpha=0.6, color='green')
    plt.plot([y_test_true.min(), y_test_true.max()], [y_test_true.min(), y_test_true.max()], 'r--', lw=2)
    plt.xlabel('Actual Prices')
    plt.ylabel('Predicted Prices')
    plt.title('Actual vs Predicted Prices')
    plt.grid(True, alpha=0.3)

    # Plot 4: Residuals
    plt.subplot(2, 2, 4)
    residuals = y_test_true.flatten() - y_test_pred_scaled.flatten()
    plt.scatter(range(len(residuals)), residuals, alpha=0.6, color='purple')
    plt.axhline(y=0, color='red', linestyle='--')
    plt.xlabel('Sample Index')
    plt.ylabel('Residuals')
    plt.title('Prediction Residuals')
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(model_dir, 'enhanced_model_analysis.png'), dpi=150, bbox_inches='tight')
    plt.close()

    # Create volume analysis plot
    plt.figure(figsize=(15, 8))

    # Plot price and volume
    plt.subplot(2, 1, 1)
    plt.plot(test_dates, y_test_true, color='blue', label='Actual Price')
    plt.plot(test_dates, y_test_pred_scaled, color='red', label='Predicted Price')
    plt.title('Price Prediction with Volume Analysis')
    plt.ylabel('Price ($)')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # Plot volume
    plt.subplot(2, 1, 2)
    test_volume_original = volume_scaler.inverse_transform(test_volume_scaled[window_size:].reshape(-1, 1))
    plt.plot(test_dates, test_volume_original, color='orange', label='Volume')
    plt.ylabel('Volume')
    plt.xlabel('Date')
    plt.legend()
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(model_dir, 'price_volume_analysis.png'), dpi=150, bbox_inches='tight')
    plt.close()

    return final_model, price_scaler, full_price_scaled, window_size, closedf, dates

def print_enhancement_summary():
    """Print summary of the Advanced Multivariate LSTM model"""
    print("\n" + "="*80)
    print("ADVANCED MULTIVARIATE LSTM BITCOIN PRICE PREDICTION MODEL")
    print("="*80)
    print("\nModel Features:")
    print("1. ✓ Advanced LSTM with functional API architecture")
    print("2. ✓ Multivariate input: Price + Volume data")
    print("3. ✓ 3-year test data validation for accuracy calculation")
    print("4. ✓ Full dataset retraining after testing")
    print("5. ✓ Comprehensive performance metrics (RMSE, MAE, R², MAPE, Accuracy)")
    print("6. ✓ Early stopping and learning rate reduction")
    print("7. ✓ Enhanced data preprocessing with sequence creation")
    print("8. ✓ Advanced visualization with multiple analysis plots")
    print("9. ✓ Model and metrics saved to /model folder")
    print("10. ✓ JSON metrics export for API access")
    print("\nKey Features:")
    print("- Model Type: Advanced Multivariate LSTM only")
    print("- Input Features: Bitcoin Price + Trading Volume")
    print("- Test Strategy: Latest 3 years for accuracy validation")
    print("- Final Training: Full historical dataset")
    print("- Output Location: /model folder (consolidated)")
    print("\nTraining Strategy:")
    print("- Step 1: Test on latest 3 years of data")
    print("- Step 2: Calculate accuracy percentage for frontend display")
    print("- Step 3: Retrain with full dataset for production model")
    print("\nExpected Benefits:")
    print("- High accuracy through advanced LSTM architecture")
    print("- Enhanced predictions with volume data integration")
    print("- Reliable accuracy metrics from 3-year test validation")
    print("- Optimized performance with full dataset training")
    print("- Consolidated model storage in /model folder")
    print("- API-ready metrics for frontend accuracy display")
    print("="*80)

# Main execution
if __name__ == "__main__":
    print_enhancement_summary()
    print("\nStarting Advanced Multivariate LSTM training process...")

    # Train the model
    train_and_save_model()

    print("\n" + "="*80)
    print("TRAINING COMPLETED SUCCESSFULLY!")
    print("="*80)
    print("\nModel files saved to /model folder:")
    print("- Final model: model/bitcoin_lstm_model.keras")
    print("- Price scaler: model/bitcoin_price_scaler.save")
    print("- Volume scaler: model/bitcoin_volume_scaler.save")
    print("- Performance metrics: model/model_metrics.json")
    print("- Visualizations: model/enhanced_model_analysis.png")
    print("- Volume analysis: model/price_volume_analysis.png")
    print("- Loss curves: model/loss_curves.png")
    print("\nThe model maintains full compatibility with existing API.")
    print("Accuracy metrics are available for frontend display via API.")
    print("="*80)