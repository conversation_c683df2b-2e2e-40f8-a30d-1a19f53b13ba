export interface PredictionResponse {
  [key: string]: {
    dates: string[];
    predicted_prices: number[];
  };
}

export type TimeFrame = '1m' | '6m' | '1y' | '3y';

export interface TimeFrameMapping {
  [key: string]: number;
}

export interface ChartData {
  dates: string[];
  prices: number[];
}

export interface ModelMetrics {
  test_accuracy: number;
  test_rmse: number;
  test_mae: number;
  test_r2: number;
  test_mape: number;
  model_type: string;
  training_data_points: number;
  test_data_points: number;
  features: string[];
  error?: string;
}