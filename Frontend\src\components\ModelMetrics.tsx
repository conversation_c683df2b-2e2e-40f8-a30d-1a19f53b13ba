import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { fetchModelMetrics } from '../api/predictionApi';
import { ModelMetrics as ModelMetricsType } from '../types';

const MetricsContainer = styled.div`
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(247, 147, 26, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
  margin: 1rem 0;
  backdrop-filter: blur(10px);
`;

const MetricsTitle = styled.h3`
  color: var(--accent);
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const MetricsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
`;

const MetricCard = styled.div`
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(247, 147, 26, 0.2);
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
`;

const MetricValue = styled.div`
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--accent);
  margin-bottom: 0.25rem;
`;

const MetricLabel = styled.div`
  font-size: 0.9rem;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const AccuracyHighlight = styled.div`
  background: linear-gradient(135deg, rgba(247, 147, 26, 0.2), rgba(247, 147, 26, 0.1));
  border: 2px solid var(--accent);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  margin-bottom: 1rem;
`;

const AccuracyValue = styled.div`
  font-size: 2.5rem;
  font-weight: bold;
  color: var(--accent);
  margin-bottom: 0.5rem;
`;

const AccuracyLabel = styled.div`
  font-size: 1rem;
  color: var(--text-primary);
  font-weight: 500;
`;

const ModelInfo = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 1rem;
  font-size: 0.9rem;
  color: var(--text-secondary);
`;

const InfoItem = styled.span`
  background: rgba(247, 147, 26, 0.1);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  border: 1px solid rgba(247, 147, 26, 0.3);
`;

const LoadingSpinner = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  color: var(--text-secondary);
`;

const ErrorMessage = styled.div`
  color: #ff6b6b;
  text-align: center;
  padding: 1rem;
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.3);
  border-radius: 8px;
`;

const ModelMetrics: React.FC = () => {
  const [metrics, setMetrics] = useState<ModelMetricsType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadMetrics = async () => {
      try {
        setLoading(true);
        const data = await fetchModelMetrics();
        setMetrics(data);
        setError(null);
      } catch (err: any) {
        setError(err.message || 'Failed to load model metrics');
        console.error('Error loading model metrics:', err);
      } finally {
        setLoading(false);
      }
    };

    loadMetrics();
  }, []);

  if (loading) {
    return (
      <MetricsContainer>
        <LoadingSpinner>Loading model performance metrics...</LoadingSpinner>
      </MetricsContainer>
    );
  }

  if (error || !metrics) {
    return (
      <MetricsContainer>
        <ErrorMessage>
          {error || 'Unable to load model metrics. Please ensure the model has been trained.'}
        </ErrorMessage>
      </MetricsContainer>
    );
  }

  return (
    <MetricsContainer>
      <MetricsTitle>
        🎯 Model Performance Metrics
      </MetricsTitle>
      
      {metrics.test_accuracy > 0 && (
        <AccuracyHighlight>
          <AccuracyValue>{metrics.test_accuracy.toFixed(2)}%</AccuracyValue>
          <AccuracyLabel>Model Accuracy</AccuracyLabel>
        </AccuracyHighlight>
      )}

      <MetricsGrid>
        <MetricCard>
          <MetricValue>{metrics.test_rmse.toFixed(4)}</MetricValue>
          <MetricLabel>RMSE</MetricLabel>
        </MetricCard>
        
        <MetricCard>
          <MetricValue>{metrics.test_mae.toFixed(4)}</MetricValue>
          <MetricLabel>MAE</MetricLabel>
        </MetricCard>
        
        <MetricCard>
          <MetricValue>{metrics.test_r2.toFixed(4)}</MetricValue>
          <MetricLabel>R² Score</MetricLabel>
        </MetricCard>
        
        <MetricCard>
          <MetricValue>{(metrics.test_mape * 100).toFixed(2)}%</MetricValue>
          <MetricLabel>MAPE</MetricLabel>
        </MetricCard>
      </MetricsGrid>

      <ModelInfo>
        <InfoItem>Model: {metrics.model_type}</InfoItem>
        <InfoItem>Features: {metrics.features.join(', ')}</InfoItem>
        <InfoItem>Training Data: {metrics.training_data_points.toLocaleString()} points</InfoItem>
        <InfoItem>Test Data: {metrics.test_data_points.toLocaleString()} points</InfoItem>
      </ModelInfo>
    </MetricsContainer>
  );
};

export default ModelMetrics;
